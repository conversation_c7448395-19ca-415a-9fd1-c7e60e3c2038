#include "camerastream.h"
#include <QDebug>
#include <QWidget>
#include "mainwindow.h"
#include <QGuiApplication>
#include <QScreen>
#include <QDateTime>
#include <QDir>
#include <QFileInfo>
CameraStream::CameraStream(QObject *parent)
    : QObject{parent}
{
    if (!gst_is_initialized()) {
        gst_init(nullptr, nullptr);
    }

    // 初始化录像相关变量
    isRecording = false;
    m_cached_record_branch = nullptr;
    m_record_tee_pad = nullptr;
    m_record_queue_pad = nullptr;

    // 初始化水印相关变量
    m_timeoverlay = nullptr;
    m_convert = nullptr;
    channelstream = 0;

    // 初始化拍照相关变量
    m_photoBranchCreated = false;

    // 获取MainWindow指针
    m_mainWindow = qobject_cast<MainWindow*>(parent);
}

CameraStream::~CameraStream()
{
    // 清理录像分支
    if (m_cached_record_branch) {
        destroyRecordBranch();
    }

    // 清理拍照分支
    if (m_photoBranchCreated) {
        destroyPhotoBranch();
    }
}

bool CameraStream::start_camera()
{

    // 创建GStreamer管道元素
    pipeline = gst_pipeline_new("camera-pipeline");
    GstElement *source = gst_element_factory_make("v4l2src", "source");
    GstElement *filter = gst_element_factory_make("capsfilter", "filter");
    m_convert = gst_element_factory_make("videoconvert", "convert");
    // 添加tee元素，用于分流视频流 (保留tee以便后续动态添加拍照分支)
    tee = gst_element_factory_make("tee", "tee");
    // 主视频流分支（简化，去掉强制格式转换）
    GstElement *queue_main = gst_element_factory_make("queue", "queue_main");
    flip = gst_element_factory_make("videoflip", "flip");
    GstElement *preview_convert = gst_element_factory_make("videoconvert", "preview_convert");
    GstElement *sink = gst_element_factory_make("waylandsink", "preview_waylandsink");
    parser = gst_element_factory_make("identity", "parser");
    decoder = gst_element_factory_make("mppjpegdec", "decoder");
        if (!pipeline || !source || !filter || !parser || !decoder || !m_convert ||
        !tee || !flip || !preview_convert || !sink || !queue_main) {
        qWarning("unableToCreateGStreamerElement");
        return false;
    }
    // 配置视频源
    g_object_set(source,
                 "device", qPrintable(CAMERA),
                 "io-mode", 4, // DMA buffer mode
                 "queue-size", 2,      // 只能设 2~8，设最小
                 "drop-buffers", TRUE, // 上游满时丢帧
                 nullptr);
    g_object_set(decoder,
             "low-delay", TRUE,    // rkvdec/mpp 特有属性
             "max-frames", 1,      // 只留 1 帧
             nullptr);
    GstCaps *caps = nullptr;
    caps = gst_caps_new_simple("image/jpeg",
                                "width", G_TYPE_INT, WIDTH,
                                "height", G_TYPE_INT, HEIGHT,
                                "framerate", GST_TYPE_FRACTION, FRAMERATE, 1,
                                nullptr);
    g_object_set(filter, "caps", caps, nullptr);
    gst_caps_unref(caps);

    // 主屏幕
    QScreen *screen = QGuiApplication::primaryScreen();

    // 整个屏幕的完整几何（含任务栏、Dock、菜单栏等）
    QRect full = screen->geometry();
    qDebug() << "屏幕完整区域：" << full;

    // 屏幕的“可用”几何（去掉系统任务栏/Dock后的区域）
    QRect avail = screen->availableGeometry();
    qDebug() << "可用区域：" << avail;

    int x = avail.x();
    int y = avail.y();
    int w = avail.width();
    int h = avail.height();
    printf("x=%d,y=%d,w=%d,h=%d\n",x,y,w,h);
    // 设置渲染区域
    g_object_set(sink,
                 "sync", FALSE,  // 禁用同步（减少延迟）
                 "enable-last-sample", FALSE,  // 不保留最后一帧
                 "layer",2,
                 nullptr);

    g_object_set(flip, "video-direction", 0, nullptr);
    // 配置队列
    g_object_set(queue_main,
                 "max-size-buffers", 3,
                 "max-size-time", 0,
                 "max-size-bytes", 0,
                 "leaky", 2, // 下游丢帧
                 NULL);

    // 定义矩形坐标和尺寸
    gint rect[4] = {x, y, w, h};  // x, y, width, height
    // 初始化GValue为GstValueArray类型
    GValue val = G_VALUE_INIT;
    g_value_init(&val, GST_TYPE_ARRAY);

    // 添加四个整数值到数组中
    for (int i = 0; i < 4; i++) {
        GValue elem = G_VALUE_INIT;
        g_value_init(&elem, G_TYPE_INT);
        g_value_set_int(&elem, rect[i]);
        gst_value_array_append_value(&val, &elem);
        g_value_unset(&elem);
    }
    // 设置属性
    g_object_set_property(G_OBJECT(sink), "render-rectangle", &val);
    // 清理
    g_value_unset(&val);

    // 构建管道
    gst_bin_add_many(GST_BIN(pipeline),
                     source, filter, parser, decoder, m_convert, tee,
                     queue_main, flip, preview_convert, sink,
                     nullptr);
    // 初始化时不添加时间水印，通过动态插入/移除来控制
    // 默认链接：source -> filter -> parser -> decoder -> m_convert -> tee
    if (!gst_element_link_many(source, filter, parser, decoder, m_convert, tee, nullptr)) {
        qWarning("无法链接GStreamer元素到tee");
        return false;
    }
    // 链接主视频流分支：queue_main -> flip -> preview_convert -> sink
    if (!gst_element_link_many(queue_main, flip, preview_convert, sink, nullptr)) {
        qWarning("无法链接主视频流分支");
        return false;
    }

    // 链接tee到主视频流队列
    GstPadTemplate *tee_src_pad_template = gst_element_class_get_pad_template(GST_ELEMENT_GET_CLASS(tee), "src_%u");

    // 主视频流pad
    GstPad *tee_main_pad = gst_element_request_pad(tee, tee_src_pad_template, NULL, NULL);
    GstPad *queue_main_pad = gst_element_get_static_pad(queue_main, "sink");

    // 链接pad
    if (gst_pad_link(tee_main_pad, queue_main_pad) != GST_PAD_LINK_OK) {
        gst_object_unref(tee_main_pad);
        gst_object_unref(queue_main_pad);
        qWarning("无法链接tee pad到主视频流");
        return false;
    }
    // 解引用不再需要的pad
    gst_object_unref(queue_main_pad);
    gst_object_unref(tee_main_pad);

    // // 设置总线消息监听
    // GstBus *bus = gst_pipeline_get_bus(GST_PIPELINE(pipeline));
    // gst_bus_add_watch(bus, busCallback, this);
    // gst_object_unref(bus);

    // 启动管道
    GstStateChangeReturn ret = gst_element_set_state(pipeline, GST_STATE_PLAYING);
    if (ret == GST_STATE_CHANGE_FAILURE) {
        qWarning("unableToStartPipeline");
        return false;
    }

    // 初始化完成后，延迟恢复时间水印状态
    QTimer::singleShot(200, this, &CameraStream::restoreTimeWatermarkOnInit);

    return true;
}

// 创建录像分支
GstElement* CameraStream::createRecordBranch(const QString &filePath)
{
    if (!pipeline || !tee) {
        printf("无法创建录像分支：pipeline或tee未初始化\n");
        return nullptr;
    }

    printf("正在创建录像分支...\n");

    // 创建录像分支的元素
    GstElement *queue_rec = gst_element_factory_make("queue", "queue_rec");
    GstElement *encoder = gst_element_factory_make("mpph264enc", "encoder");
    GstElement *h264parse = gst_element_factory_make("h264parse", "h264parse");
    GstElement *splitmuxsink = gst_element_factory_make("splitmuxsink", "splitmuxsink");

    if (!queue_rec || !encoder || !h264parse || !splitmuxsink) {
        printf("无法创建录像分支的元素\n");
        if (queue_rec) gst_object_unref(queue_rec);
        if (encoder) gst_object_unref(encoder);
        if (h264parse) gst_object_unref(h264parse);
        if (splitmuxsink) gst_object_unref(splitmuxsink);
        return nullptr;
    }

    // 配置队列
    g_object_set(queue_rec,
                 "max-size-buffers", 60,
                 "max-size-time", 1 * GST_SECOND,
                 "max-size-bytes", 0,
                 "leaky", 2,
                 "flush-on-eos", TRUE,    // EOS时刷新队列
                 NULL);

    // 配置编码器
    g_object_set(encoder,
                 "bps", 5000000,  // 5Mbps码率
                 "gop", 30,
                 "profile", 66,   // Baseline profile
                 "rc-mode", 1,    // CBR模式
                 NULL);

    // 配置splitmuxsink
    g_object_set(splitmuxsink,
                 "location", filePath.toUtf8().constData(),
                 "muxer", gst_element_factory_make("mp4mux", "muxer"),
                 NULL);

    // 将元素添加到管道
    gst_bin_add_many(GST_BIN(pipeline), queue_rec, encoder, h264parse, splitmuxsink, NULL);

    // 链接元素：queue_rec -> encoder -> h264parse -> splitmuxsink
    bool linkSuccess = gst_element_link_many(queue_rec, encoder, h264parse, splitmuxsink, NULL);
    if (!linkSuccess) {
        printf("无法链接录像分支元素\n");
        gst_bin_remove_many(GST_BIN(pipeline), queue_rec, encoder, h264parse, splitmuxsink, NULL);
        return nullptr;
    }

    // 从tee获取pad
    GstPadTemplate *tee_src_pad_template = gst_element_class_get_pad_template(GST_ELEMENT_GET_CLASS(tee), "src_%u");
    m_record_tee_pad = gst_element_request_pad(tee, tee_src_pad_template, NULL, NULL);
    m_record_queue_pad = gst_element_get_static_pad(queue_rec, "sink");

    // 存储引用到splitmuxsink的私有数据中，以便后续清理
    g_object_set_data(G_OBJECT(splitmuxsink), "queue", queue_rec);
    g_object_set_data(G_OBJECT(splitmuxsink), "encoder", encoder);
    g_object_set_data(G_OBJECT(splitmuxsink), "h264parse", h264parse);

    // 同步元素状态
    gst_element_sync_state_with_parent(queue_rec);
    gst_element_sync_state_with_parent(encoder);
    gst_element_sync_state_with_parent(h264parse);
    gst_element_sync_state_with_parent(splitmuxsink);

    printf("录像分支创建成功\n");
    return splitmuxsink;
}

// 销毁录像分支
void CameraStream::destroyRecordBranch()
{
    if (!m_cached_record_branch) {
        printf("录像分支已为空，无需销毁\n");
        return;
    }

    if (!pipeline) {
        printf("管道已为空，直接清理录像分支引用\n");
        m_cached_record_branch = nullptr;
        m_record_tee_pad = nullptr;
        m_record_queue_pad = nullptr;
        return;
    }

    printf("正在销毁录像分支...\n");

    // 先停用录像分支
    deactivateRecordBranch();

    // 获取之前存储的元素引用
    GstElement *queue_rec = (GstElement*)g_object_get_data(G_OBJECT(m_cached_record_branch), "queue");
    GstElement *encoder = (GstElement*)g_object_get_data(G_OBJECT(m_cached_record_branch), "encoder");
    GstElement *h264parse = (GstElement*)g_object_get_data(G_OBJECT(m_cached_record_branch), "h264parse");

    if (queue_rec && encoder && h264parse) {
        // 将元素状态设置为NULL
        printf("设置录像分支元素状态为NULL...\n");
        gst_element_set_state(m_cached_record_branch, GST_STATE_NULL);
        gst_element_set_state(h264parse, GST_STATE_NULL);
        gst_element_set_state(encoder, GST_STATE_NULL);
        gst_element_set_state(queue_rec, GST_STATE_NULL);

        // 等待状态变化完成
        gst_element_get_state(m_cached_record_branch, NULL, NULL, 1 * GST_SECOND);
    }

    // 安全地释放pad引用
    if (m_record_tee_pad && tee) {
        printf("释放tee的请求pad...\n");
        gst_element_release_request_pad(tee, m_record_tee_pad);
        gst_object_unref(m_record_tee_pad);
        m_record_tee_pad = nullptr;
    }

    if (m_record_queue_pad) {
        gst_object_unref(m_record_queue_pad);
        m_record_queue_pad = nullptr;
    }

    // 从管道中移除元素
    if (queue_rec && encoder && h264parse) {
        printf("从管道中移除录像分支元素...\n");
        gst_bin_remove_many(GST_BIN(pipeline), queue_rec, encoder, h264parse, m_cached_record_branch, NULL);
    } else {
        printf("部分元素引用丢失，尝试单独移除splitmuxsink...\n");
        gst_bin_remove(GST_BIN(pipeline), m_cached_record_branch);
    }

    m_cached_record_branch = nullptr;
    printf("录像分支已销毁\n");
}

// 激活录像分支（链接tee_pad和queue_pad）
bool CameraStream::activateRecordBranch()
{
    printf("开始激活录像分支...\n");

    if (!m_record_tee_pad || !m_record_queue_pad) {
        printf("无法激活录像分支：pad未初始化 (tee_pad=%p, queue_pad=%p)\n",
               m_record_tee_pad, m_record_queue_pad);
        return false;
    }

    if (!tee || !m_cached_record_branch) {
        printf("无法激活录像分支：tee或录像分支未初始化 (tee=%p, record_branch=%p)\n",
               tee, m_cached_record_branch);
        return false;
    }

    // 检查pad是否已经连接
    if (gst_pad_is_linked(m_record_tee_pad)) {
        printf("录像分支pad已连接，无需重复激活\n");
        isRecording = true;
        return true;
    }

    printf("准备链接tee pad到录像分支...\n");

    // 激活pad
    gst_pad_set_active(m_record_tee_pad, TRUE);

    // 链接pad
    GstPadLinkReturn link_result = gst_pad_link(m_record_tee_pad, m_record_queue_pad);
    if (link_result != GST_PAD_LINK_OK) {
        printf("无法链接tee到录像分支，错误代码: %d\n", link_result);
        return false;
    }

    printf("录像分支pad连接成功\n");
    printf("录像分支已激活\n");
    isRecording = true;

    return true;
}

// 停用录像分支（断开tee_pad和queue_pad）
bool CameraStream::deactivateRecordBranch()
{
    if (!m_record_tee_pad || !m_record_queue_pad) {
        printf("录像分支pad未初始化或已释放\n");
        isRecording = false;
        return true;
    }

    // 检查连接状态并断开
    if (gst_pad_is_linked(m_record_tee_pad)) {
        printf("断开录像分支pad连接...\n");
        if (gst_pad_unlink(m_record_tee_pad, m_record_queue_pad)) {
            printf("录像分支pad连接已断开\n");
        } else {
            printf("警告：断开录像分支pad连接失败\n");
        }
    } else {
        printf("录像分支pad已断开或未连接\n");
    }

    printf("录像分支已停用\n");
    isRecording = false;

    return true;
}

// 开始录像
void CameraStream::startRecording()
{
    // 防止重复调用
    if (isRecording) {
        printf("录像已在进行中，忽略重复调用\n");
        return;
    }

    printf("开始录像...\n");

    // 确保摄像头已经初始化
    if (!pipeline || !tee) {
        printf("摄像头未初始化，无法开始录像\n");
        return;
    }

    // 生成录像文件路径
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd_hh-mm-ss");
    QString fileName = QString("recording_%1.mp4").arg(timestamp);

    // 创建录像目录
    QString recordingDir = "/tmp/recordings";
    QDir dir;
    if (!dir.exists(recordingDir)) {
        dir.mkpath(recordingDir);
    }

    QString filePath = recordingDir + "/" + fileName;
    recordingfilename = filePath;

    printf("录像文件路径: %s\n", qPrintable(filePath));

    // 检查是否已经有录像分支
    if (m_cached_record_branch) {
        destroyRecordBranch();
    }

    // 检查是否需要录制音频
    bool enableAudio = false;
    if (m_mainWindow) {
        enableAudio = m_mainWindow->curaudiostate;
    }

    // 先创建音频分支（如果需要），但不激活
    bool audioSuccess = false;
    if (enableAudio) {
        printf("尝试创建音频分支，使用设备: hw:2,0\n");
        if (createAudioBranch("hw:2,0")) {
            printf("音频分支创建成功\n");
            audioSuccess = true;
        } else {
            printf("无法创建音频分支，继续进行视频录制\n");
        }
    } else {
        printf("音频录制未启用\n");
    }

    // 创建新的录像分支
    m_cached_record_branch = createRecordBranch(filePath);
    if (!m_cached_record_branch) {
        printf("无法创建录像分支\n");
        if (audioSuccess) {
            destroyAudioBranch();
        }
        return;
    }

    // 激活录像分支
    if (!activateRecordBranch()) {
        printf("无法激活录像分支\n");
        destroyRecordBranch();
        if (audioSuccess) {
            destroyAudioBranch();
        }
        return;
    }

    // 激活音频分支（如果已创建）
    if (audioSuccess) {
        printf("尝试激活音频分支...\n");
        if (activateAudioBranch()) {
            printf("音频分支已成功激活\n");
        } else {
            printf("无法激活音频分支，继续进行视频录制\n");
            destroyAudioBranch();
            audioSuccess = false;
        }
    }

    if (enableAudio && !audioSuccess) {
        printf("音频录制失败，但视频录制将继续\n");
    }

    // 禁用相机控制，防止录像时修改设置
    if (m_mainWindow) {
        m_mainWindow->disableCameraControls();
    }

    printf("录像已开始，文件: %s\n", qPrintable(filePath));
}

// 停止录像
void CameraStream::stopRecording()
{
    // 防止重复调用
    if (!isRecording) {
        printf("录像未在进行中，忽略停止调用\n");
        return;
    }

    printf("停止录像...\n");

    // 发送EOS信号确保录像文件正确关闭
    if (m_cached_record_branch) {
        printf("正在结束录像，确保正确关闭文件...\n");

        // 获取录像分支的所有元素
        GstElement *queue_rec = (GstElement*)g_object_get_data(G_OBJECT(m_cached_record_branch), "queue");
        GstElement *encoder = (GstElement*)g_object_get_data(G_OBJECT(m_cached_record_branch), "encoder");
        GstElement *h264parse = (GstElement*)g_object_get_data(G_OBJECT(m_cached_record_branch), "h264parse");

        if (queue_rec && encoder && h264parse) {
            // 设置probe阻止新数据通过
            if (m_record_tee_pad && gst_pad_is_linked(m_record_tee_pad)) {
                printf("阻止新数据进入录像分支...\n");
                gulong probe_id = gst_pad_add_probe(m_record_tee_pad,
                                                    GST_PAD_PROBE_TYPE_BLOCK_DOWNSTREAM,
                                                    (GstPadProbeCallback)NULL, NULL, NULL);

                // 让已有数据有时间流入分支
                g_usleep(100000); // 等待100ms

                // 发送EOS信号到视频和音频分支
                bool video_eos_sent = false;
                bool audio_eos_sent = false;

                // 向音频分支发送EOS（如果存在）
                if (m_audio_source) {
                    printf("向音频分支发送EOS...\n");
                    if (gst_element_send_event(m_audio_source, gst_event_new_eos())) {
                        printf("向音频源发送EOS成功\n");
                        audio_eos_sent = true;
                    } else if (m_audio_queue && gst_element_send_event(m_audio_queue, gst_event_new_eos())) {
                        printf("向音频队列发送EOS成功\n");
                        audio_eos_sent = true;
                    } else if (m_audio_convert && gst_element_send_event(m_audio_convert, gst_event_new_eos())) {
                        printf("向音频转换器发送EOS成功\n");
                        audio_eos_sent = true;
                    } else {
                        printf("向音频分支发送EOS失败\n");
                    }
                }

                // 向视频分支发送EOS
                if (gst_element_send_event(queue_rec, gst_event_new_eos())) {
                    printf("向queue_rec发送EOS成功\n");
                    video_eos_sent = true;
                } else {
                    printf("向queue_rec发送EOS失败，尝试其他元素\n");

                    // 如果失败，尝试向encoder发送
                    if (gst_element_send_event(encoder, gst_event_new_eos())) {
                        printf("向encoder发送EOS成功\n");
                        video_eos_sent = true;
                    } else {
                        printf("向encoder发送EOS失败，尝试h264parse\n");

                        // 再尝试向h264parse发送
                        if (gst_element_send_event(h264parse, gst_event_new_eos())) {
                            printf("向h264parse发送EOS成功\n");
                            video_eos_sent = true;
                        } else {
                            printf("向h264parse发送EOS失败，尝试muxsink\n");

                            // 最后尝试向splitmuxsink发送
                            if (gst_element_send_event(m_cached_record_branch, gst_event_new_eos())) {
                                printf("向splitmuxsink发送EOS成功\n");
                                video_eos_sent = true;
                            } else {
                                printf("所有视频EOS发送尝试均失败\n");
                            }
                        }
                    }
                }

                // 检查EOS发送结果
                bool eos_sent = video_eos_sent || audio_eos_sent;
                printf("EOS发送结果: 视频=%s, 音频=%s\n",
                       video_eos_sent ? "成功" : "失败",
                       audio_eos_sent ? "成功" : "失败");

                if (eos_sent) {
                    // 等待EOS信号处理完成
                    printf("等待EOS处理完成...\n");

                    // 等待状态变化，确保元素开始处理EOS
                    gst_element_get_state(m_cached_record_branch, NULL, NULL, 300 * GST_MSECOND);

                    // 等待EOS或错误消息
                    GstBus* bus = gst_element_get_bus(pipeline);
                    GstMessage* msg = gst_bus_timed_pop_filtered(bus, 100 * GST_MSECOND,
                                                                 (GstMessageType)(GST_MESSAGE_EOS | GST_MESSAGE_ERROR));

                    if (msg) {
                        switch (GST_MESSAGE_TYPE(msg)) {
                        case GST_MESSAGE_EOS:
                            printf("EOS处理完成，录像已正常结束\n");
                            break;
                        case GST_MESSAGE_ERROR:
                            printf("处理EOS时发生错误，但继续清理\n");
                            break;
                        default:
                            break;
                        }
                        gst_message_unref(msg);
                    } else {
                        printf("等待EOS超时，但可能已成功处理\n");
                    }

                    gst_object_unref(bus);

                    // 给一些时间让所有数据写入完成
                    printf("等待所有数据写入完成...\n");
                    g_usleep(100000); // 等待300ms
                }

                // 移除probe
                if (m_record_tee_pad) {
                    gst_pad_remove_probe(m_record_tee_pad, probe_id);
                }
            }
        }

        // 销毁录像分支
        destroyRecordBranch();
    }

    // 清理音频分支（如果存在）
    if (m_audio_source || m_audio_queue || m_audio_convert || m_audio_encoder || m_audio_parser) {
        printf("清理音频分支\n");
        destroyAudioBranch();
    }



    // 重新启用相机控制
    if (m_mainWindow) {
        m_mainWindow->enableCameraControls();
    }

    printf("录像已停止\n");
}

// 动态插入时间水印元素
bool CameraStream::insertTimeWatermark()
{
    if (!pipeline || !m_convert || !tee) {
        printf("无法插入时间水印：必要元素未初始化\n");
        return false;
    }

    if (m_timeoverlay) {
        printf("时间水印已存在，无需重复插入\n");
        return true;
    }

    printf("开始动态插入时间水印元素...\n");

    // 创建时间水印元素
    m_timeoverlay = gst_element_factory_make("clockoverlay", "global_timeoverlay");
    if (!m_timeoverlay) {
        printf("无法创建时间水印元素\n");
        return false;
    }

    // 配置时间水印
    g_object_set(m_timeoverlay,
                 "halignment", 2, // 右对齐
                 "time-format", "%Y-%m-%d %H:%M:%S",
                 nullptr);

    // 暂停管道以进行重新链接
    GstState current_state;
    gst_element_get_state(pipeline, &current_state, NULL, 0);
    gst_element_set_state(pipeline, GST_STATE_PAUSED);

    // 等待状态变化完成
    gst_element_get_state(pipeline, NULL, NULL, GST_CLOCK_TIME_NONE);

    // 安全地断开m_convert和tee的连接
    GstPad *convert_src = gst_element_get_static_pad(m_convert, "src");
    GstPad *tee_sink = gst_element_get_static_pad(tee, "sink");

    if (convert_src && tee_sink && gst_pad_is_linked(convert_src)) {
        gst_pad_unlink(convert_src, tee_sink);
    }

    if (convert_src) gst_object_unref(convert_src);
    if (tee_sink) gst_object_unref(tee_sink);

    // 将时间水印元素添加到管道
    gst_bin_add(GST_BIN(pipeline), m_timeoverlay);

    // 重新链接：m_convert -> timeoverlay -> tee
    if (!gst_element_link_many(m_convert, m_timeoverlay, tee, nullptr)) {
        printf("无法链接时间水印元素\n");

        // 清理失败的元素
        gst_element_set_state(m_timeoverlay, GST_STATE_NULL);
        gst_bin_remove(GST_BIN(pipeline), m_timeoverlay);
        m_timeoverlay = nullptr;

        // 恢复原始连接
        gst_element_link(m_convert, tee);
        gst_element_set_state(pipeline, current_state);
        return false;
    }

    // 同步元素状态
    gst_element_sync_state_with_parent(m_timeoverlay);

    // 恢复管道状态
    gst_element_set_state(pipeline, current_state);

    printf("时间水印元素插入成功\n");
    return true;
}

// 动态移除时间水印元素
bool CameraStream::removeTimeWatermark()
{
    if (!pipeline || !m_convert || !tee) {
        printf("无法移除时间水印：必要元素未初始化\n");
        return false;
    }

    if (!m_timeoverlay) {
        printf("时间水印不存在，无需移除\n");
        return true;
    }

    printf("开始动态移除时间水印元素...\n");

    // 暂停管道以进行重新链接
    GstState current_state;
    gst_element_get_state(pipeline, &current_state, NULL, 0);
    gst_element_set_state(pipeline, GST_STATE_PAUSED);

    // 等待状态变化完成
    gst_element_get_state(pipeline, NULL, NULL, GST_CLOCK_TIME_NONE);

    // 安全地断开连接（检查连接是否存在）
    GstPad *convert_src = gst_element_get_static_pad(m_convert, "src");
    GstPad *timeoverlay_sink = gst_element_get_static_pad(m_timeoverlay, "sink");
    GstPad *timeoverlay_src = gst_element_get_static_pad(m_timeoverlay, "src");
    GstPad *tee_sink = gst_element_get_static_pad(tee, "sink");

    if (convert_src && timeoverlay_sink && gst_pad_is_linked(convert_src)) {
        gst_pad_unlink(convert_src, timeoverlay_sink);
    }
    if (timeoverlay_src && tee_sink && gst_pad_is_linked(timeoverlay_src)) {
        gst_pad_unlink(timeoverlay_src, tee_sink);
    }

    // 释放pad引用
    if (convert_src) gst_object_unref(convert_src);
    if (timeoverlay_sink) gst_object_unref(timeoverlay_sink);
    if (timeoverlay_src) gst_object_unref(timeoverlay_src);
    if (tee_sink) gst_object_unref(tee_sink);

    // 设置时间水印元素状态为NULL并移除
    gst_element_set_state(m_timeoverlay, GST_STATE_NULL);
    gst_element_get_state(m_timeoverlay, NULL, NULL, GST_CLOCK_TIME_NONE);

    // 从bin中移除（这会自动管理引用计数）
    gst_bin_remove(GST_BIN(pipeline), m_timeoverlay);
    m_timeoverlay = nullptr;

    // 直接连接m_convert和tee
    if (!gst_element_link(m_convert, tee)) {
        printf("无法重新连接m_convert和tee\n");
        gst_element_set_state(pipeline, current_state);
        return false;
    }

    // 恢复管道状态
    gst_element_set_state(pipeline, current_state);

    printf("时间水印元素移除成功\n");
    return true;
}

// 根据timewatermark状态动态更新时间水印
void CameraStream::updateTimeWatermark()
{
    if (!pipeline) {
        printf("管道未初始化，无法更新时间水印\n");
        return;
    }

    // 检查管道状态，避免在清理过程中操作
    GstState current_state;
    GstStateChangeReturn ret = gst_element_get_state(pipeline, &current_state, NULL, 0);
    if (ret == GST_STATE_CHANGE_FAILURE || current_state == GST_STATE_NULL) {
        printf("管道状态异常，跳过时间水印更新\n");
        return;
    }

    MainWindow* m_mmainWindow = qobject_cast<MainWindow*>(parent());
    if (!m_mmainWindow) {
        printf("无法获取MainWindow引用\n");
        return;
    }

    int currentChannel = channelstream;
    if (currentChannel < 0 || currentChannel >= 4) {
        printf("无效的通道索引: %d\n", currentChannel);
        return;
    }

    bool enableTimeWatermark = m_mmainWindow->get_Watermark();
    bool currentlyHasWatermark = (m_timeoverlay != nullptr);

    printf("=== 时间水印状态检查 ===\n");
    printf("通道: %d, 期望状态: %s, 当前状态: %s\n",
           currentChannel,
           enableTimeWatermark ? "启用" : "禁用",
           currentlyHasWatermark ? "已有水印" : "无水印");

    if (enableTimeWatermark && !currentlyHasWatermark) {
        // 需要启用但当前没有，插入时间水印
        printf("→ 执行操作：插入时间水印元素\n");
        if (insertTimeWatermark()) {
            printf("✓ 时间水印插入成功\n");
        } else {
            printf("✗ 时间水印插入失败\n");
        }
    } else if (!enableTimeWatermark && currentlyHasWatermark) {
        // 需要禁用但当前有，移除时间水印
        printf("→ 执行操作：移除时间水印元素\n");
        if (removeTimeWatermark()) {
            printf("✓ 时间水印移除成功\n");
        } else {
            printf("✗ 时间水印移除失败\n");
        }
    } else {
        // 状态一致，无需操作
        printf("→ 状态一致，无需更改\n");
    }
    printf("=== 时间水印状态检查完成 ===\n");
}

// 初始化后恢复时间水印状态
void CameraStream::restoreTimeWatermarkOnInit()
{
    printf("=== 初始化后恢复时间水印状态 ===\n");

    if (!pipeline) {
        return;
    }

    // 检查管道状态
    GstState current_state;
    GstStateChangeReturn ret = gst_element_get_state(pipeline, &current_state, NULL, 100 * GST_MSECOND);

    if (ret == GST_STATE_CHANGE_FAILURE) {
        printf("管道状态获取失败，延迟恢复时间水印\n");
        QTimer::singleShot(300, this, &CameraStream::restoreTimeWatermarkOnInit);
        return;
    }

    if (current_state != GST_STATE_PLAYING) {
        printf("管道未处于PLAYING状态(%s)，延迟恢复时间水印\n",
               gst_element_state_get_name(current_state));
        QTimer::singleShot(300, this, &CameraStream::restoreTimeWatermarkOnInit);
        return;
    }

    printf("管道状态正常，开始恢复时间水印状态\n");
    updateTimeWatermark();
}

// 创建音频分支
bool CameraStream::createAudioBranch(const QString &audioDevice)
{
    if (!pipeline) {
        printf("无法创建音频分支：pipeline未初始化\n");
        return false;
    }

    if (audioDevice.isEmpty()) {
        printf("音频设备路径为空，跳过音频分支创建\n");
        return true; // 不是错误，只是没有音频
    }

    printf("正在创建音频分支，设备: %s\n", qPrintable(audioDevice));

    // 创建音频元素
    m_audio_source = gst_element_factory_make("alsasrc", "audio_source");
    m_audio_queue = gst_element_factory_make("queue", "audio_queue");
    m_audio_convert = gst_element_factory_make("audioconvert", "audio_convert");
    m_audio_encoder = gst_element_factory_make("voaacenc", "audio_encoder");
    m_audio_parser = gst_element_factory_make("aacparse", "audio_parser");

    if (!m_audio_source || !m_audio_queue || !m_audio_convert || !m_audio_encoder || !m_audio_parser) {
        printf("无法创建音频分支的元素\n");
        destroyAudioBranch();
        return false;
    }

    // 配置音频源 - 固定使用 hw:2,0
    g_object_set(m_audio_source,
                 "device", "hw:2,0",
                 NULL);

    // 配置音频队列
    g_object_set(m_audio_queue,
                 "max-size-buffers", 200,
                 "max-size-time", 2 * GST_SECOND,
                 "max-size-bytes", 0,
                 "leaky", 2, // 下游丢帧
                 NULL);

    // 配置音频编码器
    g_object_set(m_audio_encoder,
                 "bitrate", 128000, // 128kbps
                 NULL);

    // 将音频元素添加到管道
    gst_bin_add_many(GST_BIN(pipeline), m_audio_source, m_audio_queue,
                     m_audio_convert, m_audio_encoder, m_audio_parser, NULL);

    // 简化音频链接，让GStreamer自动协商格式
    if (!gst_element_link_many(m_audio_source, m_audio_queue, m_audio_convert,
                               m_audio_encoder, m_audio_parser, NULL)) {
        printf("无法链接音频分支元素\n");
        destroyAudioBranch();
        return false;
    }

    // 同步元素状态
    gst_element_sync_state_with_parent(m_audio_source);
    gst_element_sync_state_with_parent(m_audio_queue);
    gst_element_sync_state_with_parent(m_audio_convert);
    gst_element_sync_state_with_parent(m_audio_encoder);
    gst_element_sync_state_with_parent(m_audio_parser);

    m_audio_device_path = audioDevice;
    printf("音频分支创建成功\n");

    return true;
}

// 销毁音频分支
void CameraStream::destroyAudioBranch()
{
    if (!m_audio_source && !m_audio_queue && !m_audio_convert && !m_audio_encoder && !m_audio_parser) {
        return; // 没有音频分支需要销毁
    }

    printf("正在销毁音频分支...\n");

    // 停止音频元素
    if (m_audio_source) {
        gst_element_set_state(m_audio_source, GST_STATE_NULL);
    }
    if (m_audio_queue) {
        gst_element_set_state(m_audio_queue, GST_STATE_NULL);
    }
    if (m_audio_convert) {
        gst_element_set_state(m_audio_convert, GST_STATE_NULL);
    }
    if (m_audio_encoder) {
        gst_element_set_state(m_audio_encoder, GST_STATE_NULL);
    }
    if (m_audio_parser) {
        gst_element_set_state(m_audio_parser, GST_STATE_NULL);
    }

    // 从管道中移除音频元素
    if (pipeline) {
        if (m_audio_source) gst_bin_remove(GST_BIN(pipeline), m_audio_source);
        if (m_audio_queue) gst_bin_remove(GST_BIN(pipeline), m_audio_queue);
        if (m_audio_convert) gst_bin_remove(GST_BIN(pipeline), m_audio_convert);
        if (m_audio_encoder) gst_bin_remove(GST_BIN(pipeline), m_audio_encoder);
        if (m_audio_parser) gst_bin_remove(GST_BIN(pipeline), m_audio_parser);
    }

    // 清理引用
    m_audio_source = nullptr;
    m_audio_queue = nullptr;
    m_audio_convert = nullptr;
    m_audio_encoder = nullptr;
    m_audio_parser = nullptr;
    m_audio_device_path.clear();

    printf("音频分支已销毁\n");
}

// 激活音频分支（连接到splitmuxsink）
bool CameraStream::activateAudioBranch()
{
    if (!m_audio_parser || !m_cached_record_branch) {
        printf("无法激活音频分支：音频parser或录像分支未初始化\n");
        return false;
    }

    // 等待音频元素准备就绪
    gst_element_get_state(m_audio_parser, NULL, NULL, 1 * GST_SECOND);

    // 获取音频parser的src pad
    GstPad *audio_src_pad = gst_element_get_static_pad(m_audio_parser, "src");
    if (!audio_src_pad) {
        printf("无法获取音频parser的src pad\n");
        return false;
    }

    // 获取splitmuxsink的audio sink pad
    GstPad *mux_audio_pad = gst_element_get_request_pad(m_cached_record_branch, "audio_%u");
    if (!mux_audio_pad) {
        printf("无法获取splitmuxsink的audio pad\n");
        gst_object_unref(audio_src_pad);
        return false;
    }

    // 链接音频到muxer
    GstPadLinkReturn link_result = gst_pad_link(audio_src_pad, mux_audio_pad);
    if (link_result != GST_PAD_LINK_OK) {
        printf("无法链接音频到muxer，错误代码: %d\n", link_result);

        // 打印更详细的错误信息
        switch (link_result) {
        case GST_PAD_LINK_WRONG_HIERARCHY:
            printf("错误：pad不在同一管道中\n");
            break;
        case GST_PAD_LINK_WAS_LINKED:
            printf("错误：pad已经连接\n");
            break;
        case GST_PAD_LINK_WRONG_DIRECTION:
            printf("错误：pad方向错误\n");
            break;
        case GST_PAD_LINK_NOFORMAT:
            printf("错误：pad格式不兼容\n");
            break;
        case GST_PAD_LINK_NOSCHED:
            printf("错误：调度器问题\n");
            break;
        case GST_PAD_LINK_REFUSED:
            printf("错误：连接被拒绝\n");
            break;
        default:
            printf("错误：未知连接错误\n");
            break;
        }

        gst_object_unref(audio_src_pad);
        gst_object_unref(mux_audio_pad);
        return false;
    }

    printf("音频分支已成功连接到splitmuxsink\n");

    // 清理pad引用
    gst_object_unref(audio_src_pad);
    gst_object_unref(mux_audio_pad);

    return true;
}

// 创建拍照用的分支和appsink
GstElement* CameraStream::createPhotoSink()
{
    if (!pipeline || !tee) return nullptr;

    if (m_photoBranchCreated) {
        return gst_bin_get_by_name(GST_BIN(pipeline), "photo_sink");
    }

    GstElement* valve = gst_element_factory_make("valve", "photo_valve");
    GstElement* queue = gst_element_factory_make("queue", "queue_photo");
    GstElement* convert = gst_element_factory_make("videoconvert", "convert_photo");
    GstElement* appsink = gst_element_factory_make("appsink", "photo_sink");

    if (!valve || !queue || !convert || !appsink) {
        qWarning() << "拍照元素创建失败";
        return nullptr;
    }

    GstCaps* caps = gst_caps_new_simple("video/x-raw",
                                        "format", G_TYPE_STRING, "RGB",
                                        NULL);
    g_object_set(appsink,
                 "emit-signals", FALSE,
                 "sync", FALSE,
                 "drop", TRUE,
                 "max-buffers", 1,
                 "caps", caps,
                 NULL);
    gst_caps_unref(caps);

    g_object_set(valve, "drop", TRUE, NULL); // 默认关闭

    gst_bin_add_many(GST_BIN(pipeline), valve, queue, convert, appsink, NULL);
    gst_element_link_many(valve, queue, convert, appsink, NULL);

    GstPadTemplate* templ = gst_element_class_get_pad_template(GST_ELEMENT_GET_CLASS(tee), "src_%u");
    GstPad* tee_pad = gst_element_request_pad(tee, templ, NULL, NULL);
    GstPad* valve_sink_pad = gst_element_get_static_pad(valve, "sink");
    gst_pad_link(tee_pad, valve_sink_pad);
    gst_object_unref(tee_pad);
    gst_object_unref(valve_sink_pad);

    gst_element_sync_state_with_parent(valve);
    gst_element_sync_state_with_parent(queue);
    gst_element_sync_state_with_parent(convert);
    gst_element_sync_state_with_parent(appsink);

    m_photoBranchCreated = true;
    return appsink;
}

void CameraStream::destroyPhotoBranch()
{
    if (!pipeline) {
        qWarning() << "无法销毁拍照分支：pipeline 为空";
        return;
    }

    printf("正在销毁拍照分支...\n");

    // 通过名称查找元素
    GstElement* valve = gst_bin_get_by_name(GST_BIN(pipeline), "photo_valve");
    GstElement* queue = gst_bin_get_by_name(GST_BIN(pipeline), "queue_photo");
    GstElement* convert = gst_bin_get_by_name(GST_BIN(pipeline), "convert_photo");
    GstElement* appsink = gst_bin_get_by_name(GST_BIN(pipeline), "photo_sink");

    if (!valve || !queue || !convert || !appsink) {
        printf("拍照分支元素不存在或已被清理\n");
        m_photoBranchCreated = false;
        return;
    }

    // 设置元素状态为NULL
    gst_element_set_state(appsink, GST_STATE_NULL);
    gst_element_set_state(convert, GST_STATE_NULL);
    gst_element_set_state(queue, GST_STATE_NULL);
    gst_element_set_state(valve, GST_STATE_NULL);

    // 从管道中移除元素
    gst_bin_remove_many(GST_BIN(pipeline), valve, queue, convert, appsink, NULL);

    // 清理引用
    gst_object_unref(valve);
    gst_object_unref(queue);
    gst_object_unref(convert);
    gst_object_unref(appsink);

    m_photoBranchCreated = false;
    printf("拍照分支已销毁\n");
}





void CameraStream::takePhoto()
{

    // ✅ 创建拍照分支（只一次）
    GstElement* appsink = createPhotoSink();
    if (!appsink) {
        qWarning() << "无法创建拍照分支";
        return;
    }

    // ✅ 获取 valve 并打开
    GstElement* valve = gst_bin_get_by_name(GST_BIN(pipeline), "photo_valve");
    if (!valve) {
        qWarning() << "找不到 valve";
        return;
    }

    g_object_set(valve, "drop", FALSE, NULL); // 打开阀门

    // ✅ 拉取一帧
    GstSample* sample = gst_app_sink_pull_sample(GST_APP_SINK(appsink));
    if (sample) {
        GstBuffer* buffer = gst_sample_get_buffer(sample);
        GstCaps* caps = gst_sample_get_caps(sample);
        if (buffer && caps) {
            GstMapInfo map;
            if (gst_buffer_map(buffer, &map, GST_MAP_READ)) {
                GstStructure* s = gst_caps_get_structure(caps, 0);
                int width = 0, height = 0;
                gst_structure_get_int(s, "width", &width);
                gst_structure_get_int(s, "height", &height);

                QString basePath = "/tmp/photos";
                QString filePath = basePath + "/" +
                                   QDateTime::currentDateTime().toString("yyyy-MM-dd_hh-mm-ss-zzz") + ".jpg";

                QImage img(map.data, width, height, QImage::Format_RGB888);
                img.save(filePath, "JPG", 85);
                qDebug() << "photo successlly:" << filePath;

                gst_buffer_unmap(buffer, &map);
            }
        }
        gst_sample_unref(sample);
    }

    // ✅ 关闭阀门
    g_object_set(valve, "drop", TRUE, NULL);
    gst_object_unref(valve);
}

// 清理摄像头数据
void CameraStream::cleanupCameraData()
{
    printf("开始清理摄像头数据...\n");

    // 停止录像（如果正在录像）
    if (isRecording) {
        stopRecording();
    }

    // 销毁拍照分支
    if (m_photoBranchCreated) {
        printf("清理拍照分支\n");
        destroyPhotoBranch();
    }

    // 清理管道
    if (pipeline) {
        gst_element_set_state(pipeline, GST_STATE_NULL);
        gst_object_unref(pipeline);
        pipeline = nullptr;
    }

    // 重置其他元素指针
    tee = nullptr;
    flip = nullptr;
    parser = nullptr;
    decoder = nullptr;
    m_convert = nullptr;
    m_timeoverlay = nullptr;

    printf("摄像头数据清理完成\n");
}

